'use client';

import React from 'react';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useRouter } from 'next/navigation';
import { Hero } from "@/components/sections/Hero";
import { Stats } from "@/components/sections/Stats";
import { Pricing } from "@/components/sections/Pricing";
import { Testimonial } from "@/components/sections/Testimonial";
import { CTA } from "@/components/sections/CTA";
import { FAQ } from "@/components/sections/FAQ";
import { ContentFlowFooter } from "./Footer";
import {
  BarChart3,
  Calendar,
  Users,
  TrendingUp,
  Star,
  Zap,
  Globe
} from 'lucide-react';

export const ContentFlowLandingPage = () => {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/zh/auth/signin');
  };

  const handleWatchDemo = () => {
    // TODO: 添加演示视频或产品演示的逻辑
    // 可以打开模态框显示产品演示视频或跳转到演示页面
  };

  // Hero section data
  const heroData = {
    title: "让内容创作变得简单高效",
    subtitle: "AI驱动的跨平台内容创作与发布管理工具",
    description: "统一管理多个社交平台，AI智能优化内容，数据驱动决策。专为内容创作者打造的一站式解决方案。",
    cta: {
      primary: "免费开始使用",
      secondary: "观看演示"
    }
  };

  // Stats section data
  const statsData = {
    title: "数据说话，效果显著",
    subtitle: "已有数万创作者选择ContentFlow，显著提升内容表现",
    stats: [
      {
        value: "50K+",
        label: "活跃用户",
        description: "来自全球的内容创作者"
      },
      {
        value: "2M+",
        label: "内容发布",
        description: "每月通过平台发布的内容"
      },
      {
        value: "85%",
        label: "效率提升",
        description: "用户平均内容创作效率提升"
      },
      {
        value: "4.9/5",
        label: "用户评分",
        description: "基于1000+用户评价"
      }
    ]
  };

  // Pricing section data
  const pricingData = {
    title: "选择适合您的方案",
    subtitle: "从个人创作者到企业团队，我们都有合适的解决方案",
    plans: [
      {
        name: "免费版",
        price: "¥0",
        period: "/月",
        description: "适合个人创作者开始使用",
        features: [
          "每月10篇AI生成内容",
          "3个社交平台连接",
          "基础数据分析",
          "社区支持"
        ],
        cta: "免费开始",
        popular: false
      },
      {
        name: "创作者版",
        price: "¥29",
        period: "/月",
        description: "适合专业内容创作者",
        features: [
          "每月100篇AI生成内容",
          "10个社交平台连接",
          "高级数据分析",
          "内容排程功能",
          "优先客服支持"
        ],
        cta: "立即升级",
        popular: true
      },
      {
        name: "团队版",
        price: "¥99",
        period: "/月",
        description: "适合小型团队协作",
        features: [
          "无限AI生成内容",
          "无限平台连接",
          "团队协作功能",
          "高级分析报告",
          "API访问权限",
          "专属客服支持"
        ],
        cta: "联系销售",
        popular: false
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white via-rose-50/20 to-pink-50/10 dark:from-background dark:via-rose-950/5 dark:to-pink-950/5">
      {/* Hero Section */}
      <Hero
        hero={heroData}
        onPrimaryClick={handleGetStarted}
        onSecondaryClick={handleWatchDemo}
      />

      {/* Elegant Section Divider */}
      <div className="relative py-8">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-rose-200/50 dark:border-rose-800/30"></div>
        </div>
        <div className="relative flex justify-center">
          <div className="bg-white dark:bg-background px-6">
            <div className="w-3 h-3 bg-gradient-to-r from-rose-300 to-pink-300 rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <section id="features" className="py-24 wedding-gradient-bg relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(244,63,94,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(244,63,94,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.1),transparent_50%)]"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-20">
            <Badge className="mb-6 bg-rose-100 text-rose-800 hover:bg-rose-100 border-rose-200 dark:bg-rose-950/50 dark:text-rose-300 dark:border-rose-800 shadow-sm">
              <Star className="w-4 h-4 mr-1" />
              核心功能
            </Badge>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 wedding-text-gradient">
              强大的功能，简单的操作
            </h2>
            <p className="text-xl text-rose-700 dark:text-rose-200 max-w-3xl mx-auto leading-relaxed">
              集成最新AI技术，简化内容创作流程，提升创作效率和内容表现
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            <Card className="wedding-card p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-14 h-14 bg-gradient-to-br from-rose-100 to-pink-100 dark:from-rose-900/50 dark:to-pink-900/50 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Zap className="w-7 h-7 text-rose-600 dark:text-rose-400" />
              </div>
              <h3 className="text-xl font-semibold text-rose-900 dark:text-rose-100 mb-3">AI智能创作</h3>
              <p className="text-rose-700 dark:text-rose-200 leading-relaxed">
                基于GPT-4的智能文案生成，自动优化标题和内容，提升互动率和转化效果
              </p>
            </Card>

            <Card className="wedding-card p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-14 h-14 bg-gradient-to-br from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Globe className="w-7 h-7 text-pink-600 dark:text-pink-400" />
              </div>
              <h3 className="text-xl font-semibold text-rose-900 dark:text-rose-100 mb-3">多平台发布</h3>
              <p className="text-rose-700 dark:text-rose-200 leading-relaxed">
                一键发布到Instagram、TikTok、LinkedIn等多个平台，自动适配不同格式要求
              </p>
            </Card>

            <Card className="wedding-card p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-14 h-14 bg-gradient-to-br from-rose-100 to-pink-100 dark:from-rose-900/50 dark:to-pink-900/50 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <BarChart3 className="w-7 h-7 text-rose-600 dark:text-rose-400" />
              </div>
              <h3 className="text-xl font-semibold text-rose-900 dark:text-rose-100 mb-3">数据分析</h3>
              <p className="text-rose-700 dark:text-rose-200 leading-relaxed">
                深度分析内容表现，提供优化建议，帮助您制作更受欢迎的内容
              </p>
            </Card>

            <Card className="wedding-card p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-14 h-14 bg-gradient-to-br from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Calendar className="w-7 h-7 text-pink-600 dark:text-pink-400" />
              </div>
              <h3 className="text-xl font-semibold text-rose-900 dark:text-rose-100 mb-3">智能排程</h3>
              <p className="text-rose-700 dark:text-rose-200 leading-relaxed">
                基于受众活跃时间的智能发布调度，确保内容在最佳时机触达用户
              </p>
            </Card>

            <Card className="wedding-card p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-14 h-14 bg-gradient-to-br from-rose-100 to-pink-100 dark:from-rose-900/50 dark:to-pink-900/50 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <TrendingUp className="w-7 h-7 text-rose-600 dark:text-rose-400" />
              </div>
              <h3 className="text-xl font-semibold text-rose-900 dark:text-rose-100 mb-3">增长预测</h3>
              <p className="text-rose-700 dark:text-rose-200 leading-relaxed">
                AI预测内容表现，识别高潜力内容，帮助您专注于最有效的创作方向
              </p>
            </Card>

            <Card className="wedding-card p-8 hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <div className="w-14 h-14 bg-gradient-to-br from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Users className="w-7 h-7 text-pink-600 dark:text-pink-400" />
              </div>
              <h3 className="text-xl font-semibold text-rose-900 dark:text-rose-100 mb-3">团队协作</h3>
              <p className="text-rose-700 dark:text-rose-200 leading-relaxed">
                支持团队成员协作，审核流程管理，让内容创作更加高效有序
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Elegant Section Divider */}
      <div className="relative py-12">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-rose-200/50 dark:border-rose-800/30"></div>
        </div>
        <div className="relative flex justify-center">
          <div className="bg-white dark:bg-background px-6">
            <div className="flex space-x-2">
              <div className="w-2 h-2 bg-rose-300 rounded-full"></div>
              <div className="w-2 h-2 bg-pink-300 rounded-full"></div>
              <div className="w-2 h-2 bg-rose-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <Stats section={statsData} />

      {/* Elegant Section Divider */}
      <div className="relative py-12">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-rose-200/50 dark:border-rose-800/30"></div>
        </div>
        <div className="relative flex justify-center">
          <div className="bg-white dark:bg-background px-6">
            <div className="w-4 h-4 bg-gradient-to-r from-rose-300 via-pink-300 to-rose-300 rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <Pricing pricing={pricingData} />

      {/* Testimonials Section */}
      <Testimonial section={{
        title: "用户怎么说",
        subtitle: "听听我们用户的真实反馈",
        testimonials: [
          {
            content: "ContentFlow彻底改变了我的内容创作流程。AI生成的内容质量很高，多平台发布功能节省了我大量时间。",
            author: {
              name: "张小明",
              title: "自媒体博主",
              company: "个人工作室",
              image: "/avatars/user1.jpg"
            }
          },
          {
            content: "作为营销团队，我们需要管理多个品牌的社交媒体。ContentFlow的团队协作功能让我们的工作效率提升了80%。",
            author: {
              name: "李经理",
              title: "市场营销总监",
              company: "科技公司",
              image: "/avatars/user2.jpg"
            }
          },
          {
            content: "数据分析功能非常强大，帮助我们优化内容策略，粉丝增长速度比以前快了3倍。",
            author: {
              name: "王创业",
              title: "电商运营",
              company: "电商企业",
              image: "/avatars/user3.jpg"
            }
          }
        ]
      }} />

      {/* CTA Section */}
      <CTA
        section={{
          title: "准备开始您的内容创作之旅？",
          subtitle: "加入数万创作者的行列，让AI助力您的内容创作",
          cta: {
            primary: "免费开始使用",
            secondary: "预约演示"
          }
        }}
        onPrimaryClick={handleGetStarted}
        onSecondaryClick={handleWatchDemo}
      />

      {/* FAQ Section */}
      <FAQ section={{
        title: "常见问题",
        subtitle: "解答您关于ContentFlow的疑问",
        faqs: [
          {
            question: "ContentFlow支持哪些社交媒体平台？",
            answer: "我们支持主流的社交媒体平台，包括微信公众号、微博、抖音、小红书、Instagram、Facebook、Twitter、LinkedIn、TikTok等。我们会持续添加更多平台支持。"
          },
          {
            question: "AI生成的内容质量如何？",
            answer: "我们使用最新的GPT-4模型，结合您的品牌调性和历史数据进行训练，生成的内容质量很高。同时支持人工编辑和优化，确保内容符合您的要求。"
          },
          {
            question: "是否支持团队协作？",
            answer: "是的，我们提供完整的团队协作功能，包括角色权限管理、内容审核流程、评论协作等，让团队成员可以高效协作。"
          },
          {
            question: "数据安全如何保障？",
            answer: "我们采用企业级安全标准，所有数据都经过加密存储和传输。我们通过了SOC 2认证，严格遵守数据保护法规。"
          },
          {
            question: "可以免费试用吗？",
            answer: "是的，我们提供14天免费试用，无需信用卡。试用期内您可以体验所有核心功能。"
          }
        ]
      }} />

      {/* Footer */}
      <ContentFlowFooter />
    </div>
  );
};
