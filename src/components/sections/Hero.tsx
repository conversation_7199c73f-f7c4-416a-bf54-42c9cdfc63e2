"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";

interface HeroProps {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
  onPrimaryClick?: () => void;
  onSecondaryClick?: () => void;
}

export function Hero({ hero, onPrimaryClick, onSecondaryClick }: HeroProps) {
  return (
    <section id="hero" className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      {/* Wedding-themed Background with romantic gradients */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-background via-rose-50/30 to-pink-50/20 dark:from-background dark:via-rose-950/20 dark:to-pink-950/10" />
        {/* Romantic radial gradients */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_800px_at_50%_200px,rgba(244,63,94,0.08),transparent)] dark:bg-[radial-gradient(circle_800px_at_50%_200px,rgba(244,63,94,0.15),transparent)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_400px_at_80%_100px,rgba(236,72,153,0.06),transparent)] dark:bg-[radial-gradient(circle_400px_at_80%_100px,rgba(236,72,153,0.12),transparent)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_300px_at_20%_300px,rgba(251,113,133,0.05),transparent)] dark:bg-[radial-gradient(circle_300px_at_20%_300px,rgba(251,113,133,0.1),transparent)]" />
      </div>

      {/* Elegant grid background with wedding theme */}
      <div className="absolute inset-0 -z-10">
        <div className="h-full w-full bg-[linear-gradient(to_right,rgba(244,63,94,0.03)_1px,transparent_1px),linear-gradient(to_bottom,rgba(244,63,94,0.03)_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,rgba(251,113,133,0.05)_1px,transparent_1px),linear-gradient(to_bottom,rgba(251,113,133,0.05)_1px,transparent_1px)] bg-[size:60px_60px]" />
      </div>

      {/* Romantic floating particles effect */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-rose-300/30 dark:bg-rose-400/50 rounded-full animate-bounce" style={{ animationDelay: '0s', animationDuration: '4s' }} />
        <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-pink-300/35 dark:bg-pink-400/60 rounded-full animate-bounce" style={{ animationDelay: '1.5s', animationDuration: '5s' }} />
        <div className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-rose-200/25 dark:bg-rose-300/45 rounded-full animate-bounce" style={{ animationDelay: '3s', animationDuration: '6s' }} />
        <div className="absolute top-2/3 right-1/3 w-1 h-1 bg-pink-200/30 dark:bg-pink-300/50 rounded-full animate-bounce" style={{ animationDelay: '2s', animationDuration: '4.5s' }} />
      </div>

      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-10 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-6 max-w-4xl mx-auto"
          >
            <h1 className="responsive-text-hero font-bold tracking-tighter bg-clip-text text-transparent bg-gradient-to-r from-rose-600 via-pink-500 to-rose-400 dark:from-rose-300 dark:via-pink-300 dark:to-rose-200 leading-tight">
              {hero.title}
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-rose-700 dark:text-rose-200 max-w-4xl mx-auto font-medium leading-relaxed">
              {hero.subtitle}
            </p>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-rose-600 dark:text-rose-300 max-w-3xl mx-auto leading-relaxed">
              {hero.description}
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            className="flex flex-col sm:flex-row items-center justify-center gap-6"
          >
            <Button
              size="lg"
              onClick={onPrimaryClick}
              className="mobile-button bg-gradient-to-r from-rose-400 to-pink-400 hover:from-rose-500 hover:to-pink-500 text-white rounded-full px-8 sm:px-12 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0"
            >
              {hero.cta.primary}
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={onSecondaryClick}
              className="mobile-button rounded-full px-8 sm:px-12 font-semibold border-2 border-rose-300 text-rose-700 hover:bg-rose-50 dark:border-rose-600 dark:text-rose-300 dark:hover:bg-rose-950/30 transition-all duration-300 transform hover:scale-105"
            >
              {hero.cta.secondary}
            </Button>
          </motion.div>

          {/* Trust indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-wrap items-center justify-center gap-8 text-sm text-gray-500 dark:text-gray-400 mt-8"
          >
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span>50K+ 活跃创作者</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span>2M+ 内容发布</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
              <span>99.9% 服务可用性</span>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
